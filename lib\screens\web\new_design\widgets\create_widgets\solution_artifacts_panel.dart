import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/models/solution/solution_session_model.dart';
import 'package:nsl/screens/web/new_design/widgets/create_widgets/brd_enhanced.dart';
import 'package:nsl/screens/web/new_design/widgets/create_widgets/brd_parse_artifact.dart';
import 'package:nsl/theme/app_colors.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:visibility_detector/visibility_detector.dart';
import '../../../../../theme/spacing.dart';

class SolutionArtifactsPanel extends StatefulWidget {
  final SolutionSessionModel? solutionSessionModel;

  /// Callback when the close button is pressed
  final VoidCallback? onClose;

  /// Chat controller for interactive elements
  final TextEditingController? chatController;

  /// Callback when a message is sent
  final VoidCallback? onSendMessage;
  final Map<String, dynamic>? brdData;

  const SolutionArtifactsPanel({
    super.key,
    this.solutionSessionModel,
    this.brdData,
    this.onClose,
    this.chatController,
    this.onSendMessage,
  });

  @override
  State<SolutionArtifactsPanel> createState() => _SolutionArtifactsPanelState();
}

class _SolutionArtifactsPanelState extends State<SolutionArtifactsPanel> {
  // Track expanded state for each section
  Set<String> expandedSectionIds = <String>{};

  // Track active section for navigation highlighting
  String? activeSectionId;

  // ScrollController for the content area
  late ScrollController _scrollController;

  // Variables for fixing scrolling issue (similar to role_details_panel)
  double _bottomSpacerHeight = 500;

  // Global keys for sections - moved to class level to persist across builds
  late Map<String, GlobalKey> sectionKeys;

  // VisibilityInfo objects for each section
  Map<String, VisibilityInfo> sectionVisibilityInfo = {};

  @override
  void initState() {
    super.initState();

    // Initialize scroll controller
    _scrollController = ScrollController();

    // Initialize section keys
    sectionKeys = {
      'sm': GlobalKey(),
      'os': GlobalKey(),
      'de': GlobalKey(),
      'bp': GlobalKey(),
      'fo': GlobalKey(),
      'ux': GlobalKey(),
      'ia': GlobalKey(),
      'pm': GlobalKey(),
      'fm': GlobalKey(),
      'sc': GlobalKey(),
      'ic': GlobalKey(),
      'cc': GlobalKey(),
      'sr': GlobalKey(),
    };

    // Initialize the first section as active
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        activeSectionId = 'sm';
      });
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Define the sections for navigation with their abbreviations
    final Map<String, String> sectionMap = {
      'sm': "SM",
      'os': "OS",
      'de': "DE",
      'bp': "BP",
      'fo': "FO",
      'ux': "UX",
      'ia': "IA",
      'pm': "PM",
      'fm': "FM",
      'sc': "SC",
      'ic': "IC",
      'cc': "CC",
      'sr': "SR",
    };

    // Enhanced scroll function that fixes the bottom sections scrolling issue
    void scrollToSection(String sectionId) {
      if (sectionKeys.containsKey(sectionId)) {
        final key = sectionKeys[sectionId];

        if (key?.currentContext != null) {
          // Update active section immediately
          setState(() {
            activeSectionId = sectionId;
          });

          // Update bottom spacer height for better scrolling
          setState(() {
            final sectionKeysList = sectionMap.keys.toList();
            final sectionIndex = sectionKeysList.indexOf(sectionId);

            // Calculate required bottom padding based on section position
            if (sectionIndex >= (sectionKeysList.length - 6)) {
              // Last 6 sections need more space
              _bottomSpacerHeight = MediaQuery.of(context).size.height - 100;
            } else {
              _bottomSpacerHeight = 500;
            }
          });

          // Scroll to the section
          Future.delayed(Duration(milliseconds: 50), () {
            if (_scrollController.hasClients) {
              Scrollable.ensureVisible(
                key!.currentContext!,
                alignment: 0.0,
                duration: Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
            }
          });
        }
      }
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          left: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
        boxShadow: [
          BoxShadow(
            color: Color(0xff9B9B9B).withValues(alpha: 0.14),
            blurRadius: 20,
            offset: Offset(-3, 0),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and close button
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Text(
                          "Business Requirement Document",
                          style: FontManager.getCustomStyle(
                            fontSize: FontManager.s10,
                            fontWeight: FontWeight.normal,
                            color: AppColors.black,
                            fontFamily: FontManager.fontFamilyTiemposText,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.chat, color: Colors.black, size: 16),
                  padding: EdgeInsets.zero,
                  constraints: BoxConstraints(),
                  onPressed: () {},
                ),
                const SizedBox(
                  width: AppSpacing.xxs,
                ),
                IconButton(
                  icon: SvgPicture.asset(
                    'assets/images/chat/toggle_open_close.svg',
                    width: 20,
                    height: 20,
                    colorFilter: ColorFilter.mode(
                      Colors.grey.shade700,
                      BlendMode.srcIn,
                    ),
                  ),
                  onPressed: widget.onClose,
                  padding: EdgeInsets.zero,
                ),
              ],
            ),
          ),

          // Content area with navigation labels and sections
          // Expanded(
          //   child: Padding(
          //     padding: EdgeInsets.only(left: AppSpacing.xxs),
          //     child: Row(
          //       crossAxisAlignment: CrossAxisAlignment.start,
          //       children: [
          //         // Left column for navigation labels
          //         Container(
          //           width: 30,
          //           padding: EdgeInsets.only(top: 8),
          //           child: SingleChildScrollView(
          //             child: Column(
          //               crossAxisAlignment: CrossAxisAlignment.start,
          //               children: [
          //                 for (final entry in sectionMap.entries)
          //                   Padding(
          //                     padding: EdgeInsets.only(left: 10, top: 8),
          //                     child: MouseRegion(
          //                       cursor: SystemMouseCursors.click,
          //                       child: GestureDetector(
          //                         onTap: () => scrollToSection(entry.key),
          //                         child: Text(
          //                           entry.value,
          //                           style: TextStyle(
          //                             fontFamily: "TiemposText",
          //                             fontWeight: activeSectionId == entry.key
          //                                 ? FontWeight.bold
          //                                 : FontWeight.w400,
          //                             fontSize: 10,
          //                             color: activeSectionId == entry.key
          //                                 ? Colors.blue.shade700
          //                                 : Colors.black,
          //                             decoration: TextDecoration.underline,
          //                           ),
          //                         ),
          //                       ),
          //                     ),
          //                   ),
          //               ],
          //             ),
          //           ),
          //         ),

          //         // Right column for content
          //         Expanded(
          //           child: Scrollbar(
          //             controller: _scrollController,
          //             thumbVisibility: true,
          //             child: SingleChildScrollView(
          //               controller: _scrollController,
          //               child: Column(
          //                 crossAxisAlignment: CrossAxisAlignment.start,
          //                 children: [
          //                   _buildSummarySection(context, sectionKeys),
          //                   _buildOrganizationalSection(context, sectionKeys),
          //                   _buildDataSection(context, sectionKeys),
          //                   _buildBusinessSection(context, sectionKeys),
          //                   _buildFunctionSection(context, sectionKeys),
          //                   _buildUserInterfaceSection(context, sectionKeys),
          //                   _buildIntelligentSection(context, sectionKeys),
          //                   _buildPerformanceSection(context, sectionKeys),
          //                   _buildFinancialSection(context, sectionKeys),
          //                   _buildSecuritySection(context, sectionKeys),
          //                   _buildIntegrationSection(context, sectionKeys),
          //                   _buildCollaborationSection(context, sectionKeys),
          //                   _buildSolutionSection(context, sectionKeys),
          //                   // Smart bottom padding to fix scrolling for bottom sections
          //                   SizedBox(height: _bottomSpacerHeight),
          //                 ],
          //               ),
          //             ),
          //           ),
          //         ),
          //       ],
          //     ),
          //   ),
          // ),
          Expanded(
            // child: BRDSolutionDisplay(jsonData: widget.brdData ?? {}),
            child: EnhancedCollapsibleViewer(
              jsonData: widget.brdData ?? {},
              showAppBar: false,
            ),
          )
        ],
      ),
    );
  }

  /// Method to update active section based on visibility
  void _updateActiveSection() {
    String? mostVisibleSection;
    double maxVisibility = 0;

    sectionVisibilityInfo.forEach((sectionId, info) {
      if (info.visibleFraction > maxVisibility) {
        maxVisibility = info.visibleFraction;
        mostVisibleSection = sectionId;
      }
    });

    if (mostVisibleSection != null && mostVisibleSection != activeSectionId) {
      if (mounted) {
        setState(() {
          activeSectionId = mostVisibleSection;
        });
      }
    }
  }

  /// Builds a generic section widget with VisibilityDetector
  Widget _buildSection(
    BuildContext context,
    Map<String, GlobalKey> sectionKeys,
    String sectionId,
    String title,
    bool hasData,
    Widget Function() contentBuilder,
  ) {
    return VisibilityDetector(
      key: Key(sectionId),
      onVisibilityChanged: (VisibilityInfo info) {
        sectionVisibilityInfo[sectionId] = info;
        _updateActiveSection();
      },
      child: Container(
        key: sectionKeys[sectionId],
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '$title:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
                fontFamily: "TiemposText",
              ),
            ),
            SizedBox(height: 8),
            hasData ? contentBuilder() : _buildNoDataWidget(title),
          ],
        ),
      ),
    );
  }

  /// Builds a no data widget
  Widget _buildNoDataWidget(String sectionName) {
    return Text(
      // 'No $sectionName data available.',
      'No data available.',
      style: TextStyle(
        fontFamily: 'TiemposText',
        fontSize: 14,
        fontStyle: FontStyle.italic,
        color: Colors.grey.shade700,
      ),
    );
  }

  Widget _buildSummarySection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'sm',
      'Summary',
      widget.solutionSessionModel?.updatedNodes
              ?.any((node) => node.nodeId!.toLowerCase().contains("intro")) ??
          false,
      () => _buildSummaryContent(context),
    );
  }

  Widget _buildSummaryContent(BuildContext context) {
    final executiveSummaryValues = widget.solutionSessionModel?.updatedNodes !=
            null
        ? widget.solutionSessionModel?.updatedNodes
            ?.where((node) => node.nodeId!.toLowerCase().contains("intro"))
            .map((node) =>
                {"key": node.key as String, "value": node.value as String})
            .toList()
        : [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: executiveSummaryValues!
          .map(
            (e) => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "${e["key"]} :",
                  style: FontManager.getCustomStyle(
                      fontSize: 16,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontWeight: FontWeight.w400),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        e["value"],
                        style: FontManager.getCustomStyle(
                            fontSize: 16,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontWeight: FontWeight.w400),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: AppSpacing.xs,
                )
              ],
            ),
          )
          .toList(),
    );
  }

  Widget _buildOrganizationalSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'os',
      'Organizational Structure & Role',
      widget.solutionSessionModel?.updatedNodes
              ?.any((node) => node.nodeId!.toLowerCase().contains("sec1")) ??
          false,
      () => _buildOrganizationalContent(context),
    );
  }

  Widget _buildOrganizationalContent(BuildContext context) {
    final organizationalValues =
        widget.solutionSessionModel?.updatedNodes != null
            ? widget.solutionSessionModel?.updatedNodes
                ?.where((node) => node.nodeId!.toLowerCase().contains("sec1"))
                .map((node) =>
                    {"key": node.key as String, "value": node.value as String})
                .toList()
            : [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: organizationalValues!
          .map(
            (e) => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "${e["key"]} :",
                  style: FontManager.getCustomStyle(
                      fontSize: 16,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontWeight: FontWeight.w400),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        e["value"],
                        style: FontManager.getCustomStyle(
                            fontSize: 16,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontWeight: FontWeight.w400),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: AppSpacing.xs,
                )
              ],
            ),
          )
          .toList(),
    );
  }

  Widget _buildDataSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'de',
      'Data & Entity Management',
      widget.solutionSessionModel?.updatedNodes
              ?.any((node) => node.nodeId!.toLowerCase().contains("sec2")) ??
          false,
      () => _buildDataContent(context),
    );
  }

  Widget _buildDataContent(BuildContext context) {
    final executiveSummaryValues = widget.solutionSessionModel?.updatedNodes !=
            null
        ? widget.solutionSessionModel?.updatedNodes
            ?.where((node) => node.nodeId!.toLowerCase().contains("sec2"))
            .map((node) =>
                {"key": node.key as String, "value": node.value as String})
            .toList()
        : [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: executiveSummaryValues!
          .map(
            (e) => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "${e["key"]} :",
                  style: FontManager.getCustomStyle(
                      fontSize: 16,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontWeight: FontWeight.w400),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        e["value"],
                        style: FontManager.getCustomStyle(
                            fontSize: 16,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontWeight: FontWeight.w400),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: AppSpacing.xs,
                )
              ],
            ),
          )
          .toList(),
    );
  }

  Widget _buildBusinessSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'bp',
      'Business Process & Workflow',
      widget.solutionSessionModel?.updatedNodes
              ?.any((node) => node.nodeId!.toLowerCase().contains("sec3")) ??
          false,
      () => _buildBusinessContent(context),
    );
  }

  Widget _buildBusinessContent(BuildContext context) {
    final executiveSummaryValues = widget.solutionSessionModel?.updatedNodes !=
            null
        ? widget.solutionSessionModel?.updatedNodes
            ?.where((node) => node.nodeId!.toLowerCase().contains("sec3"))
            .map((node) =>
                {"key": node.key as String, "value": node.value as String})
            .toList()
        : [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: executiveSummaryValues!
          .map(
            (e) => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "${e["key"]} :",
                  style: FontManager.getCustomStyle(
                      fontSize: 16,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontWeight: FontWeight.w400),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        e["value"],
                        style: FontManager.getCustomStyle(
                            fontSize: 16,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontWeight: FontWeight.w400),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: AppSpacing.xs,
                )
              ],
            ),
          )
          .toList(),
    );
  }

  Widget _buildFunctionSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'fo',
      'Function & Operations',
      widget.solutionSessionModel?.updatedNodes
              ?.any((node) => node.nodeId!.toLowerCase().contains("sec4")) ??
          false,
      () => _buildFunctionContent(context),
    );
  }

  Widget _buildFunctionContent(BuildContext context) {
    final executiveSummaryValues = widget.solutionSessionModel?.updatedNodes !=
            null
        ? widget.solutionSessionModel?.updatedNodes
            ?.where((node) => node.nodeId!.toLowerCase().contains("sec4"))
            .map((node) =>
                {"key": node.key as String, "value": node.value as String})
            .toList()
        : [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: executiveSummaryValues!
          .map(
            (e) => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "${e["key"]} :",
                  style: FontManager.getCustomStyle(
                      fontSize: 16,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontWeight: FontWeight.w400),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        e["value"],
                        style: FontManager.getCustomStyle(
                            fontSize: 16,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontWeight: FontWeight.w400),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: AppSpacing.xs,
                )
              ],
            ),
          )
          .toList(),
    );
  }

  Widget _buildUserInterfaceSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'ux',
      'User Interface & Experience',
      widget.solutionSessionModel?.updatedNodes
              ?.any((node) => node.nodeId!.toLowerCase().contains("sec5")) ??
          false,
      () => _buildUserContent(context),
    );
  }

  Widget _buildUserContent(BuildContext context) {
    final executiveSummaryValues = widget.solutionSessionModel?.updatedNodes !=
            null
        ? widget.solutionSessionModel?.updatedNodes
            ?.where((node) => node.nodeId!.toLowerCase().contains("sec5"))
            .map((node) =>
                {"key": node.key as String, "value": node.value as String})
            .toList()
        : [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: executiveSummaryValues!
          .map(
            (e) => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "${e["key"]} :",
                  style: FontManager.getCustomStyle(
                      fontSize: 16,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontWeight: FontWeight.w400),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        e["value"],
                        style: FontManager.getCustomStyle(
                            fontSize: 16,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontWeight: FontWeight.w400),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: AppSpacing.xs,
                )
              ],
            ),
          )
          .toList(),
    );
  }

  Widget _buildIntelligentSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'ia',
      'Intelligence & Analytics',
      widget.solutionSessionModel?.updatedNodes
              ?.any((node) => node.nodeId!.toLowerCase().contains("sec6")) ??
          false,
      () => _buildIntelligentContent(context),
    );
  }

  Widget _buildIntelligentContent(BuildContext context) {
    final executiveSummaryValues = widget.solutionSessionModel?.updatedNodes !=
            null
        ? widget.solutionSessionModel?.updatedNodes
            ?.where((node) => node.nodeId!.toLowerCase().contains("sec6"))
            .map((node) =>
                {"key": node.key as String, "value": node.value as String})
            .toList()
        : [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: executiveSummaryValues!
          .map(
            (e) => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "${e["key"]} :",
                  style: FontManager.getCustomStyle(
                      fontSize: 16,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontWeight: FontWeight.w400),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        e["value"],
                        style: FontManager.getCustomStyle(
                            fontSize: 16,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontWeight: FontWeight.w400),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: AppSpacing.xs,
                )
              ],
            ),
          )
          .toList(),
    );
  }

  Widget _buildPerformanceSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'pm',
      'Performance & Metrics',
      widget.solutionSessionModel?.updatedNodes
              ?.any((node) => node.nodeId!.toLowerCase().contains("sec7")) ??
          false,
      () => _buildPerformanceContent(context),
    );
  }

  Widget _buildPerformanceContent(BuildContext context) {
    final executiveSummaryValues = widget.solutionSessionModel?.updatedNodes !=
            null
        ? widget.solutionSessionModel?.updatedNodes
            ?.where((node) => node.nodeId!.toLowerCase().contains("sec7"))
            .map((node) =>
                {"key": node.key as String, "value": node.value as String})
            .toList()
        : [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: executiveSummaryValues!
          .map(
            (e) => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "${e["key"]} :",
                  style: FontManager.getCustomStyle(
                      fontSize: 16,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontWeight: FontWeight.w400),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        e["value"],
                        style: FontManager.getCustomStyle(
                            fontSize: 16,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontWeight: FontWeight.w400),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: AppSpacing.xs,
                )
              ],
            ),
          )
          .toList(),
    );
  }

  Widget _buildFinancialSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'fm',
      'Financial Management',
      widget.solutionSessionModel?.updatedNodes
              ?.any((node) => node.nodeId!.toLowerCase().contains("sec8")) ??
          false,
      () => _buildFinancialContent(context),
    );
  }

  Widget _buildFinancialContent(BuildContext context) {
    final executiveSummaryValues = widget.solutionSessionModel?.updatedNodes !=
            null
        ? widget.solutionSessionModel?.updatedNodes
            ?.where((node) => node.nodeId!.toLowerCase().contains("sec8"))
            .map((node) =>
                {"key": node.key as String, "value": node.value as String})
            .toList()
        : [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: executiveSummaryValues!
          .map(
            (e) => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "${e["key"]} :",
                  style: FontManager.getCustomStyle(
                      fontSize: 16,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontWeight: FontWeight.w400),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        e["value"],
                        style: FontManager.getCustomStyle(
                            fontSize: 16,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontWeight: FontWeight.w400),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: AppSpacing.xs,
                )
              ],
            ),
          )
          .toList(),
    );
  }

  Widget _buildSecuritySection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'sc',
      'Security, Compliance & Governance',
      widget.solutionSessionModel?.updatedNodes
              ?.any((node) => node.nodeId!.toLowerCase().contains("sec9")) ??
          false,
      () => _buildSecurityContent(context),
    );
  }

  Widget _buildSecurityContent(BuildContext context) {
    final executiveSummaryValues = widget.solutionSessionModel?.updatedNodes !=
            null
        ? widget.solutionSessionModel?.updatedNodes
            ?.where((node) => node.nodeId!.toLowerCase().contains("sec9"))
            .map((node) =>
                {"key": node.key as String, "value": node.value as String})
            .toList()
        : [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: executiveSummaryValues!
          .map(
            (e) => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "${e["key"]} :",
                  style: FontManager.getCustomStyle(
                      fontSize: 16,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontWeight: FontWeight.w400),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        e["value"],
                        style: FontManager.getCustomStyle(
                            fontSize: 16,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontWeight: FontWeight.w400),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: AppSpacing.xs,
                )
              ],
            ),
          )
          .toList(),
    );
  }

  Widget _buildIntegrationSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'ic',
      'Integration & Connectivity',
      widget.solutionSessionModel?.updatedNodes
              ?.any((node) => node.nodeId!.toLowerCase().contains("sec10")) ??
          false,
      () => _buildIntegrationContent(context),
    );
  }

  Widget _buildIntegrationContent(BuildContext context) {
    final executiveSummaryValues = widget.solutionSessionModel?.updatedNodes !=
            null
        ? widget.solutionSessionModel?.updatedNodes
            ?.where((node) => node.nodeId!.toLowerCase().contains("sec10"))
            .map((node) =>
                {"key": node.key as String, "value": node.value as String})
            .toList()
        : [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: executiveSummaryValues!
          .map(
            (e) => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "${e["key"]} :",
                  style: FontManager.getCustomStyle(
                      fontSize: 16,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontWeight: FontWeight.w400),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        e["value"],
                        style: FontManager.getCustomStyle(
                            fontSize: 16,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontWeight: FontWeight.w400),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: AppSpacing.xs,
                )
              ],
            ),
          )
          .toList(),
    );
  }

  Widget _buildCollaborationSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'cc',
      'Collaboration & Communication',
      widget.solutionSessionModel?.updatedNodes
              ?.any((node) => node.nodeId!.toLowerCase().contains("sec11")) ??
          false,
      () => _buildCollaborationContent(context),
    );
  }

  Widget _buildCollaborationContent(BuildContext context) {
    final executiveSummaryValues = widget.solutionSessionModel?.updatedNodes !=
            null
        ? widget.solutionSessionModel?.updatedNodes
            ?.where((node) => node.nodeId!.toLowerCase().contains("sec11"))
            .map((node) =>
                {"key": node.key as String, "value": node.value as String})
            .toList()
        : [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: executiveSummaryValues!
          .map(
            (e) => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "${e["key"]} :",
                  style: FontManager.getCustomStyle(
                      fontSize: 16,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontWeight: FontWeight.w400),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        e["value"],
                        style: FontManager.getCustomStyle(
                            fontSize: 16,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontWeight: FontWeight.w400),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: AppSpacing.xs,
                )
              ],
            ),
          )
          .toList(),
    );
  }

  Widget _buildSolutionSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'sr',
      'Solution Readiness',
      widget.solutionSessionModel?.updatedNodes
              ?.any((node) => node.nodeId!.toLowerCase().contains("sec12")) ??
          false,
      () => _buildSolutionContent(context),
    );
  }

  Widget _buildSolutionContent(BuildContext context) {
    final executiveSummaryValues = widget.solutionSessionModel?.updatedNodes !=
            null
        ? widget.solutionSessionModel?.updatedNodes
            ?.where((node) => node.nodeId!.toLowerCase().contains("sec12"))
            .map((node) =>
                {"key": node.key as String, "value": node.value as String})
            .toList()
        : [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: executiveSummaryValues!
          .map(
            (e) => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "${e["key"]} :",
                  style: FontManager.getCustomStyle(
                      fontSize: 16,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontWeight: FontWeight.w400),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        e["value"],
                        style: FontManager.getCustomStyle(
                            fontSize: 16,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontWeight: FontWeight.w400),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: AppSpacing.xs,
                )
              ],
            ),
          )
          .toList(),
    );
  }
}
