import 'package:flutter/material.dart';
import 'package:ui_controls_library/widgets/location_widget.dart';

/// Example demonstrating the enhanced LocationWidget with search functionality
class LocationWidgetExample extends StatefulWidget {
  const LocationWidgetExample({super.key});

  @override
  State<LocationWidgetExample> createState() => _LocationWidgetExampleState();
}

class _LocationWidgetExampleState extends State<LocationWidgetExample> {
  String? _selectedAddress;
  double? _selectedLatitude;
  double? _selectedLongitude;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Enhanced Location Widget'),
        backgroundColor: const Color(0xFF0058FF),
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Enhanced Location Widget Features:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '• Type an address to see location suggestions\n'
              '• Click on suggestions to select a location\n'
              '• Click the location icon to search and show map\n'
              '• Map displays automatically when location is found',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 24),
            
            // Address Only Mode with Dynamic Map
            const Text(
              'Address Search with Dynamic Map:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            LocationWidget(
              displayMode: LocationDisplayMode.addressOnly,
              label: 'Location',
              addressHint: 'Type an address (e.g., Hyderabad, Telangana, India)',
              enableAddressSearch: true,
              showLabel: true,
              onLocationChanged: (latitude, longitude) {
                setState(() {
                  _selectedLatitude = latitude;
                  _selectedLongitude = longitude;
                });
                print('Location changed: $latitude, $longitude');
              },
              onAddressChanged: (address) {
                setState(() {
                  _selectedAddress = address;
                });
                print('Address changed: $address');
              },
              onError: (error) {
                print('Location error: $error');
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Error: $error'),
                    backgroundColor: Colors.red,
                  ),
                );
              },
            ),
            
            const SizedBox(height: 24),
            
            // Map and Address Mode
            const Text(
              'Map and Address Mode:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            LocationWidget(
              displayMode: LocationDisplayMode.mapAndAddress,
              label: 'Full Location Widget',
              addressHint: 'Search for a location',
              enableAddressSearch: true,
              showLabel: true,
              height: 200,
              mapZoom: 15.0,
              onLocationChanged: (latitude, longitude) {
                print('Full widget location: $latitude, $longitude');
              },
              onAddressChanged: (address) {
                print('Full widget address: $address');
              },
            ),
            
            const SizedBox(height: 24),
            
            // Selected Location Info
            if (_selectedAddress != null || 
                (_selectedLatitude != null && _selectedLongitude != null))
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Selected Location:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    if (_selectedAddress != null)
                      Text('Address: $_selectedAddress'),
                    if (_selectedLatitude != null && _selectedLongitude != null)
                      Text(
                        'Coordinates: ${_selectedLatitude!.toStringAsFixed(6)}, '
                        '${_selectedLongitude!.toStringAsFixed(6)}',
                      ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// Main app to run the example
class LocationWidgetExampleApp extends StatelessWidget {
  const LocationWidgetExampleApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Location Widget Example',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const LocationWidgetExample(),
      debugShowCheckedModeBanner: false,
    );
  }
}

void main() {
  runApp(const LocationWidgetExampleApp());
}
