import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:ui_controls_library/utils/callback_interpreter.dart';
import 'package:ui_controls_library/widgets/utils/font_manager.dart';

/// The position of the icon in relation to the text
enum IconPosition {
  /// Icon appears before the text
  leading,

  /// Icon appears after the text
  trailing,
}

/// The shape of the radio button
enum RadioShape {
  /// Standard circular radio button
  circle,

  /// Square radio button
  square,

  /// Diamond-shaped radio button
  diamond,
}

/// The position of the radio button relative to the label
enum RadioPosition {
  /// Radio button appears before the label
  leading,

  /// Radio button appears after the label
  trailing,
}

/// A comprehensive radio button group widget with extensive customization options.
///
/// This widget provides a group of radio buttons with various customization options
/// including layout, colors, sizes, labels, and more.
class RadioButtonWidget extends StatefulWidget {
  /// The list of options to display as radio buttons
  final List<String> options;

  /// The initially selected option (if any)
  final String? initialValue;

  /// The title or label for the radio button group
  final String? groupTitle;

  /// The style for the group title
  final TextStyle? groupTitleStyle;

  /// The alignment of the group title
  final TextAlign groupTitleAlignment;

  /// The color of the active radio button
  final Color activeColor;

  /// The color of the radio button border when inactive
  final Color inactiveColor;

  /// The color of the radio button fill when selected
  final Color? fillColor;

  /// The color of the text labels
  final Color textColor;

  /// The color of the selected option's text
  final Color? selectedTextColor;

  /// The size of the radio buttons
  final double radioSize;

  /// The font size for the option labels
  final double fontSize;

  /// The font weight for the option labels
  final FontWeight fontWeight;

  /// The font weight for the selected option label
  final FontWeight? selectedFontWeight;

  /// Whether the radio buttons are arranged horizontally
  final bool isHorizontal;

  /// Whether the radio buttons are disabled
  final bool isDisabled;

  /// Whether the radio buttons are read-only
  final bool isReadOnly;

  /// The spacing between radio buttons
  final double spacing;

  /// The padding around each radio button and label
  final EdgeInsetsGeometry padding;

  /// The margin around the entire widget
  final EdgeInsetsGeometry margin;

  /// Whether to show dividers between options
  final bool showDividers;

  /// The color of dividers between options
  final Color? dividerColor;

  /// The thickness of dividers between options
  final double dividerThickness;

  /// Whether to show a border around the entire widget
  final bool hasBorder;

  /// The color of the border around the entire widget
  final Color borderColor;

  /// The width of the border around the entire widget
  final double borderWidth;

  /// The radius of the border corners
  final double borderRadius;

  /// Whether to show a shadow under the widget
  final bool hasShadow;

  /// The elevation of the shadow
  final double elevation;

  /// The color of the shadow
  final Color? shadowColor;

  /// Whether to use a card-like appearance
  final bool isCard;

  /// The background color of the widget
  final Color backgroundColor;

  /// Whether to show a tooltip on hover
  final bool showTooltip;

  /// Custom tooltips for each option
  final Map<String, String>? tooltips;

  /// Whether to animate selection changes
  final bool hasAnimation;

  /// The duration of the selection animation
  final Duration animationDuration;

  /// Whether to wrap text in the labels
  final bool wrapText;

  /// The maximum number of lines for labels
  final int? maxLines;

  /// Whether to show icons alongside the labels
  final bool showIcons;

  /// Custom icons for each option
  final Map<String, IconData>? icons;

  /// The size of the icons
  final double iconSize;

  /// The color of the icons
  final Color? iconColor;

  /// The color of the selected option's icon
  final Color? selectedIconColor;

  /// The position of the icons relative to the text
  final IconPosition iconPosition;

  /// The spacing between the icon and text
  final double iconTextSpacing;

  /// Whether to show a "none" or "clear selection" option
  final bool allowNone;

  /// The label for the "none" option
  final String noneOptionLabel;

  /// Whether to use Material 3 design
  final bool useMaterial3;

  /// Whether to use a custom shape for the radio buttons
  final bool useCustomShape;

  /// The shape to use for the radio buttons
  final RadioShape radioShape;

  /// Whether to use a dense layout
  final bool isDense;

  /// The callback when the selection changes
  final Function(String?)? onChanged;

  /// The position of the radio button relative to the label
  final RadioPosition radioPosition;

  /// The alignment of the content within each option
  final MainAxisAlignment optionAlignment;

  /// The semantic label for screen readers
  final String? semanticLabel;

  /// Callback for mouse hover
  ///
  /// This function is called when the mouse pointer enters or exits the widget.
  /// The parameter is true when the mouse enters and false when it exits.
  final void Function(bool)? onHover;

  /// Callback for keyboard focus
  ///
  /// This function is called when the widget gains or loses keyboard focus.
  /// The parameter is true when focus is gained and false when it is lost.
  final void Function(bool)? onFocus;

  /// Color when the widget is hovered
  final Color? hoverColor;

  /// Color when the widget is focused
  final Color? focusColor;

  /// Border color when hovering over a radio button option
  final Color? hoverBorderColor;

  /// Border color when a radio button option is selected
  final Color? selectedBorderColor;

  /// Tooltip text to display on hover for the entire widget (not individual options)
  final String? tooltip;

  /// Whether to enable haptic/audio feedback
  ///
  /// If true, the widget will provide haptic and/or audio feedback when interacted with.
  final bool enableFeedback;

  /// Callback for double tap gesture
  ///
  /// This function is called when the widget is double-tapped.
  final VoidCallback? onDoubleTap;

  /// Callback for long press gesture
  ///
  /// This function is called when the widget is long-pressed.
  final VoidCallback? onLongPress;

  /// Callback for tap gesture
  ///
  /// This function is called when the widget is tapped.
  final VoidCallback? onTap;

  /// Focus node for controlling focus
  final FocusNode? focusNode;

  /// Whether the widget should autofocus
  final bool autofocus;

  /// JSON data for additional configuration
  ///
  /// This can include any custom configuration data for the radio button widget.
  final dynamic jsonConfig;

  /// Whether to use custom validation rules from JSON config
  final bool useJsonValidation;

  /// Whether to use custom styling from JSON config
  final bool useJsonStyling;

  /// Whether to use custom behavior from JSON config
  final bool useJsonBehavior;

  /// Whether to use custom options from JSON config
  final bool useJsonOptions;

  /// Custom validation message from JSON config
  final String? jsonValidationMessage;

  /// Custom option group type from JSON config (e.g., "colors", "sizes", "ratings")
  final String? jsonOptionGroupType;

  /// Dynamic callback definitions from JSON
  ///
  /// This can include callback definitions for various events:
  /// - onChanged: Executed when the selection changes
  /// - onTap: Executed when the widget is tapped
  /// - onDoubleTap: Executed when the widget is double-tapped
  /// - onLongPress: Executed when the widget is long-pressed
  /// - onHover: Executed when the mouse enters or exits the widget
  /// - onFocus: Executed when the widget gains or loses focus
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use dynamic callbacks from JSON
  final bool useJsonCallbacks;

  /// State map for dynamic callbacks
  ///
  /// This map can be used by dynamic callbacks to store and retrieve state.
  final Map<String, dynamic>? callbackState;

  /// Custom handlers for dynamic callbacks
  ///
  /// This map contains custom handler functions that can be called by dynamic callbacks.
  final Map<String, Function>? customCallbackHandlers;

  const RadioButtonWidget({
    super.key,
    this.options = const ["Option 1", "Option 2", "Option 3"],
    this.initialValue,
    this.groupTitle,
    this.groupTitleStyle,
    this.groupTitleAlignment = TextAlign.start,
    this.activeColor = const Color(0xFF0058FF),
    this.inactiveColor = Colors.grey,
    this.fillColor,
    this.textColor = Colors.black,
    this.selectedTextColor = Colors.black,
    this.radioSize = 16.0,
    this.fontSize = 14.0,
    this.fontWeight = FontWeight.normal,
    this.selectedFontWeight,
    this.isHorizontal = false,
    this.isDisabled = false,
    this.isReadOnly = false,
    this.spacing = 8.0,
    this.padding = const EdgeInsets.all(8.0),
    this.margin = EdgeInsets.zero,
    this.showDividers = false,
    this.dividerColor,
    this.dividerThickness = 1.0,
    this.hasBorder = false,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 8.0,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.shadowColor,
    this.isCard = false,
    this.backgroundColor = Colors.white,
    this.showTooltip = false,
    this.tooltips,
    this.hasAnimation = false,
    this.animationDuration = const Duration(milliseconds: 200),
    this.wrapText = true,
    this.maxLines,
    this.showIcons = false,
    this.icons,
    this.iconSize = 20.0,
    this.iconColor,
    this.selectedIconColor,
    this.iconPosition = IconPosition.leading,
    this.iconTextSpacing = 8.0,
    this.allowNone = false,
    this.noneOptionLabel = "None",
    this.useMaterial3 = false,
    this.useCustomShape = false,
    this.radioShape = RadioShape.circle,
    this.isDense = false,
    this.onChanged,
    this.radioPosition = RadioPosition.leading,
    this.optionAlignment = MainAxisAlignment.start,
    this.semanticLabel,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.hoverColor,
    this.focusColor,
    this.hoverBorderColor,
    this.selectedBorderColor,
    this.tooltip,
    this.enableFeedback = true,
    this.onDoubleTap,
    this.onLongPress,
    this.onTap,
    this.focusNode,
    this.autofocus = false,
    // JSON configuration properties
    this.jsonConfig,
    this.useJsonValidation = false,
    this.useJsonStyling = false,
    this.useJsonBehavior = false,
    this.useJsonOptions = false,
    this.jsonValidationMessage,
    this.jsonOptionGroupType,
    // JSON callback properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
  });

  /// Creates a RadioButtonWidget from a JSON map
  ///
  /// This factory constructor allows creating a RadioButtonWidget from a JSON map,
  /// making it easy to configure the widget from dynamic data.
  factory RadioButtonWidget.fromJson(Map<String, dynamic> json) {
    // Parse options
    List<String> options = ["Option 1", "Option 2", "Option 3"];
    if (json['options'] != null) {
      if (json['options'] is List) {
        options = List<String>.from(
          (json['options'] as List).map((e) => e.toString()),
        );
      } else if (json['options'] is String) {
        // Handle comma-separated string
        options =
            (json['options'] as String)
                .split(',')
                .map((e) => e.trim())
                .toList();
      }
    }

    // Parse initial value
    String? initialValue;
    if (json['initialValue'] != null) {
      initialValue = json['initialValue'].toString();
    } else if (json['selected'] != null) {
      initialValue = json['selected'].toString();
    } else if (json['defaultValue'] != null) {
      initialValue = json['defaultValue'].toString();
    }

    // Parse group title
    String? groupTitle;
    if (json['groupTitle'] != null) {
      groupTitle = json['groupTitle'].toString();
    } else if (json['title'] != null) {
      groupTitle = json['title'].toString();
    } else if (json['label'] != null) {
      groupTitle = json['label'].toString();
    }

    // Parse text alignment
    TextAlign groupTitleAlignment = TextAlign.start;
    if (json['groupTitleAlignment'] != null) {
      final alignStr = json['groupTitleAlignment'].toString().toLowerCase();
      switch (alignStr) {
        case 'center':
          groupTitleAlignment = TextAlign.center;
          break;
        case 'end':
        case 'right':
          groupTitleAlignment = TextAlign.end;
          break;
        case 'start':
        case 'left':
        default:
          groupTitleAlignment = TextAlign.start;
          break;
      }
    }

    // Parse colors
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        // Try to parse as hex
        if (colorValue.startsWith('#')) {
          try {
            final hexValue = int.parse(colorValue.substring(1), radix: 16);
            if (colorValue.length == 7) {
              // #RRGGBB format
              return Color(0xFF000000 | hexValue);
            } else if (colorValue.length == 9) {
              // #AARRGGBB format
              return Color(hexValue);
            }
          } catch (e) {
            // Ignore parsing errors and return null
          }
        }

        // Try to match color names
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Color(0xFF0058FF);
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'cyan':
            return Colors.cyan;
          case 'teal':
            return Colors.teal;
          case 'indigo':
            return Colors.indigo;
          case 'amber':
            return Colors.amber;
          case 'lime':
            return Colors.lime;
          default:
            return null;
        }
      } else if (colorValue is int) {
        return Color(colorValue);
      }

      return null;
    }

    final activeColor = parseColor(json['activeColor']) ?? Color(0xFF0058FF);
    final inactiveColor = parseColor(json['inactiveColor']) ?? Colors.grey;
    final fillColor = parseColor(json['fillColor']);
    final textColor = parseColor(json['textColor']) ?? Colors.black;
    final selectedTextColor =
        parseColor(json['selectedTextColor']) ?? Colors.black;
    final backgroundColor = parseColor(json['backgroundColor']) ?? Colors.white;
    final borderColor = parseColor(json['borderColor']) ?? Colors.grey;
    final dividerColor = parseColor(json['dividerColor']);
    final shadowColor = parseColor(json['shadowColor']);
    final iconColor = parseColor(json['iconColor']);
    final selectedIconColor = parseColor(json['selectedIconColor']);
    final hoverColor = parseColor(json['hoverColor']);
    final focusColor = parseColor(json['focusColor']);
    final hoverBorderColor = parseColor(json['hoverBorderColor']);
    final selectedBorderColor =
        parseColor(json['selectedBorderColor']) ?? Colors.red;

    // Parse numeric values
    double parseDouble(dynamic value, double defaultValue) {
      if (value == null) return defaultValue;
      if (value is num) return value.toDouble();
      if (value is String) {
        return double.tryParse(value) ?? defaultValue;
      }
      return defaultValue;
    }

    int? parseInt(dynamic value) {
      if (value == null) return null;
      if (value is int) return value;
      if (value is double) return value.toInt();
      if (value is String) {
        return int.tryParse(value);
      }
      return null;
    }

    final radioSize = parseDouble(json['radioSize'], 20.0);
    final fontSize = parseDouble(json['fontSize'], 16.0);
    final spacing = parseDouble(json['spacing'], 8.0);
    final borderWidth = parseDouble(json['borderWidth'], 1.0);
    final borderRadius = parseDouble(json['borderRadius'], 8.0);
    final elevation = parseDouble(json['elevation'], 2.0);
    final dividerThickness = parseDouble(json['dividerThickness'], 1.0);
    final iconSize = parseDouble(json['iconSize'], 20.0);
    final iconTextSpacing = parseDouble(json['iconTextSpacing'], 8.0);

    // Parse boolean values
    bool parseBool(dynamic value, bool defaultValue) {
      if (value == null) return defaultValue;
      if (value is bool) return value;
      if (value is String) {
        return value.toLowerCase() == 'true' ||
            value.toLowerCase() == 'yes' ||
            value == '1';
      }
      if (value is num) {
        return value != 0;
      }
      return defaultValue;
    }

    final isHorizontal = parseBool(json['isHorizontal'], false);
    final isDisabled = parseBool(json['isDisabled'], false);
    final isReadOnly = parseBool(json['isReadOnly'], false);
    final showDividers = parseBool(json['showDividers'], false);
    final hasBorder = parseBool(json['hasBorder'], false);
    final hasShadow = parseBool(json['hasShadow'], false);
    final isCard = parseBool(json['isCard'], false);
    final showTooltip = parseBool(json['showTooltip'], false);
    final hasAnimation = parseBool(json['hasAnimation'], false);
    final wrapText = parseBool(json['wrapText'], true);
    final showIcons = parseBool(json['showIcons'], false);
    final allowNone = parseBool(json['allowNone'], false);
    final useMaterial3 = parseBool(json['useMaterial3'], false);
    final useCustomShape = parseBool(json['useCustomShape'], false);
    final isDense = parseBool(json['isDense'], false);
    final enableFeedback = parseBool(json['enableFeedback'], true);
    final autofocus = parseBool(json['autofocus'], false);

    // Parse maxLines
    final maxLines = parseInt(json['maxLines']);

    // Parse font weights
    FontWeight? parseFontWeight(dynamic value) {
      if (value == null) return null;

      if (value is String) {
        switch (value.toLowerCase()) {
          case 'bold':
            return FontWeight.bold;
          case 'normal':
            return FontWeight.normal;
          case 'light':
            return FontWeight.w300;
          case 'medium':
            return FontWeight.w500;
          case 'semibold':
            return FontWeight.w600;
          case 'thin':
            return FontWeight.w100;
          default:
            // Try to parse as a number
            final weight = int.tryParse(value);
            if (weight != null) {
              return FontWeight.values.firstWhere(
                (fw) => fw.index == weight,
                orElse: () => FontWeight.normal,
              );
            }
            return null;
        }
      } else if (value is int) {
        return FontWeight.values.firstWhere(
          (fw) => fw.index == value,
          orElse: () => FontWeight.normal,
        );
      }

      return null;
    }

    final fontWeight = parseFontWeight(json['fontWeight']) ?? FontWeight.normal;
    final selectedFontWeight = parseFontWeight(json['selectedFontWeight']);

    // Parse padding and margin
    EdgeInsetsGeometry parsePadding(
      dynamic value,
      EdgeInsetsGeometry defaultValue,
    ) {
      if (value == null) return defaultValue;

      if (value is Map) {
        final left = parseDouble(value['left'], 8.0);
        final top = parseDouble(value['top'], 8.0);
        final right = parseDouble(value['right'], 8.0);
        final bottom = parseDouble(value['bottom'], 8.0);

        return EdgeInsets.fromLTRB(left, top, right, bottom);
      } else if (value is num) {
        return EdgeInsets.all(value.toDouble());
      } else if (value is String) {
        final padding = double.tryParse(value);
        if (padding != null) {
          return EdgeInsets.all(padding);
        }
      }

      return defaultValue;
    }

    final padding = parsePadding(json['padding'], const EdgeInsets.all(8.0));
    final margin = parsePadding(json['margin'], EdgeInsets.zero);

    // Parse animation duration
    Duration parseDuration(dynamic value, Duration defaultValue) {
      if (value == null) return defaultValue;

      if (value is int) {
        return Duration(milliseconds: value);
      } else if (value is String) {
        final ms = int.tryParse(value);
        if (ms != null) {
          return Duration(milliseconds: ms);
        }
      }

      return defaultValue;
    }

    final animationDuration = parseDuration(
      json['animationDuration'],
      const Duration(milliseconds: 200),
    );

    // Parse radio shape
    RadioShape parseRadioShape(dynamic value) {
      if (value == null) return RadioShape.circle;

      if (value is String) {
        switch (value.toLowerCase()) {
          case 'square':
            return RadioShape.square;
          case 'diamond':
            return RadioShape.diamond;
          case 'circle':
          default:
            return RadioShape.circle;
        }
      }

      return RadioShape.circle;
    }

    final radioShape = parseRadioShape(json['radioShape']);

    // Parse radio position
    RadioPosition parseRadioPosition(dynamic value) {
      if (value == null) return RadioPosition.leading;

      if (value is String) {
        switch (value.toLowerCase()) {
          case 'trailing':
            return RadioPosition.trailing;
          case 'leading':
          default:
            return RadioPosition.leading;
        }
      }

      return RadioPosition.leading;
    }

    final radioPosition = parseRadioPosition(json['radioPosition']);

    // Parse icon position
    IconPosition parseIconPosition(dynamic value) {
      if (value == null) return IconPosition.leading;

      if (value is String) {
        switch (value.toLowerCase()) {
          case 'trailing':
            return IconPosition.trailing;
          case 'leading':
          default:
            return IconPosition.leading;
        }
      }

      return IconPosition.leading;
    }

    final iconPosition = parseIconPosition(json['iconPosition']);

    // Parse option alignment
    MainAxisAlignment parseOptionAlignment(dynamic value) {
      if (value == null) return MainAxisAlignment.start;

      if (value is String) {
        switch (value.toLowerCase()) {
          case 'center':
            return MainAxisAlignment.center;
          case 'end':
            return MainAxisAlignment.end;
          case 'space_between':
            return MainAxisAlignment.spaceBetween;
          case 'space_around':
            return MainAxisAlignment.spaceAround;
          case 'space_evenly':
            return MainAxisAlignment.spaceEvenly;
          case 'start':
          default:
            return MainAxisAlignment.start;
        }
      }

      return MainAxisAlignment.start;
    }

    final optionAlignment = parseOptionAlignment(json['optionAlignment']);

    // Parse tooltips
    Map<String, String>? tooltips;
    if (json['tooltips'] != null && json['tooltips'] is Map) {
      tooltips = Map<String, String>.from(
        (json['tooltips'] as Map).map(
          (key, value) => MapEntry(key.toString(), value.toString()),
        ),
      );
    }

    // Parse icons
    Map<String, IconData>? icons;
    if (json['icons'] != null && json['icons'] is Map) {
      icons = {};
      (json['icons'] as Map).forEach((key, value) {
        IconData? iconData;
        if (value is String) {
          // Try to match common icon names
          switch (value.toLowerCase()) {
            case 'check':
              iconData = Icons.check;
              break;
            case 'close':
              iconData = Icons.close;
              break;
            case 'add':
              iconData = Icons.add;
              break;
            case 'remove':
              iconData = Icons.remove;
              break;
            case 'star':
              iconData = Icons.star;
              break;
            case 'favorite':
              iconData = Icons.favorite;
              break;
            case 'home':
              iconData = Icons.home;
              break;
            case 'settings':
              iconData = Icons.settings;
              break;
            case 'person':
              iconData = Icons.person;
              break;
            case 'info':
              iconData = Icons.info;
              break;
            case 'warning':
              iconData = Icons.warning;
              break;
            case 'error':
              iconData = Icons.error;
              break;
            case 'help':
              iconData = Icons.help;
              break;
            case 'search':
              iconData = Icons.search;
              break;
            case 'menu':
              iconData = Icons.menu;
              break;
            case 'more':
              iconData = Icons.more_horiz;
              break;
            case 'more_vert':
              iconData = Icons.more_vert;
              break;
            case 'arrow_back':
              iconData = Icons.arrow_back;
              break;
            case 'arrow_forward':
              iconData = Icons.arrow_forward;
              break;
            case 'arrow_up':
              iconData = Icons.arrow_upward;
              break;
            case 'arrow_down':
              iconData = Icons.arrow_downward;
              break;
            case 'check_circle':
              iconData = Icons.check_circle;
              break;
            case 'cancel':
              iconData = Icons.cancel;
              break;
            case 'delete':
              iconData = Icons.delete;
              break;
            case 'edit':
              iconData = Icons.edit;
              break;
            case 'save':
              iconData = Icons.save;
              break;
            case 'refresh':
              iconData = Icons.refresh;
              break;
            case 'share':
              iconData = Icons.share;
              break;
            case 'attach':
              iconData = Icons.attach_file;
              break;
            case 'camera':
              iconData = Icons.camera_alt;
              break;
            case 'photo':
              iconData = Icons.photo;
              break;
            case 'video':
              iconData = Icons.videocam;
              break;
            case 'mic':
              iconData = Icons.mic;
              break;
            case 'call':
              iconData = Icons.call;
              break;
            case 'email':
              iconData = Icons.email;
              break;
            case 'message':
              iconData = Icons.message;
              break;
            case 'location':
              iconData = Icons.location_on;
              break;
            case 'calendar':
              iconData = Icons.calendar_today;
              break;
            case 'time':
              iconData = Icons.access_time;
              break;
            case 'alarm':
              iconData = Icons.alarm;
              break;
            case 'lock':
              iconData = Icons.lock;
              break;
            case 'unlock':
              iconData = Icons.lock_open;
              break;
            case 'visibility':
              iconData = Icons.visibility;
              break;
            case 'visibility_off':
              iconData = Icons.visibility_off;
              break;
            case 'volume_up':
              iconData = Icons.volume_up;
              break;
            case 'volume_down':
              iconData = Icons.volume_down;
              break;
            case 'volume_mute':
              iconData = Icons.volume_mute;
              break;
            case 'play':
              iconData = Icons.play_arrow;
              break;
            case 'pause':
              iconData = Icons.pause;
              break;
            case 'stop':
              iconData = Icons.stop;
              break;
            case 'skip_next':
              iconData = Icons.skip_next;
              break;
            case 'skip_previous':
              iconData = Icons.skip_previous;
              break;
            case 'fast_forward':
              iconData = Icons.fast_forward;
              break;
            case 'fast_rewind':
              iconData = Icons.fast_rewind;
              break;
            case 'repeat':
              iconData = Icons.repeat;
              break;
            case 'shuffle':
              iconData = Icons.shuffle;
              break;
            case 'playlist':
              iconData = Icons.playlist_play;
              break;
            case 'cloud':
              iconData = Icons.cloud;
              break;
            case 'cloud_download':
              iconData = Icons.cloud_download;
              break;
            case 'cloud_upload':
              iconData = Icons.cloud_upload;
              break;
            case 'download':
              iconData = Icons.download;
              break;
            case 'upload':
              iconData = Icons.upload;
              break;
            case 'wifi':
              iconData = Icons.wifi;
              break;
            case 'bluetooth':
              iconData = Icons.bluetooth;
              break;
            case 'battery':
              iconData = Icons.battery_full;
              break;
            case 'power':
              iconData = Icons.power_settings_new;
              break;
            case 'brightness':
              iconData = Icons.brightness_6;
              break;
            case 'language':
              iconData = Icons.language;
              break;
            case 'translate':
              iconData = Icons.translate;
              break;
            case 'flag':
              iconData = Icons.flag;
              break;
            case 'bookmark':
              iconData = Icons.bookmark;
              break;
            case 'link':
              iconData = Icons.link;
              break;
            case 'copy':
              iconData = Icons.content_copy;
              break;
            case 'paste':
              iconData = Icons.content_paste;
              break;
            case 'cut':
              iconData = Icons.content_cut;
              break;
            case 'select_all':
              iconData = Icons.select_all;
              break;
            case 'format_bold':
              iconData = Icons.format_bold;
              break;
            case 'format_italic':
              iconData = Icons.format_italic;
              break;
            case 'format_underline':
              iconData = Icons.format_underline;
              break;
            case 'format_color':
              iconData = Icons.format_color_fill;
              break;
            case 'format_size':
              iconData = Icons.format_size;
              break;
            case 'format_align_left':
              iconData = Icons.format_align_left;
              break;
            case 'format_align_center':
              iconData = Icons.format_align_center;
              break;
            case 'format_align_right':
              iconData = Icons.format_align_right;
              break;
            case 'format_align_justify':
              iconData = Icons.format_align_justify;
              break;
            case 'format_list_bulleted':
              iconData = Icons.format_list_bulleted;
              break;
            case 'format_list_numbered':
              iconData = Icons.format_list_numbered;
              break;
            case 'format_quote':
              iconData = Icons.format_quote;
              break;
            case 'format_indent_increase':
              iconData = Icons.format_indent_increase;
              break;
            case 'format_indent_decrease':
              iconData = Icons.format_indent_decrease;
              break;
            case 'print':
              iconData = Icons.print;
              break;
            case 'attach_money':
              iconData = Icons.attach_money;
              break;
            case 'credit_card':
              iconData = Icons.credit_card;
              break;
            case 'shopping_cart':
              iconData = Icons.shopping_cart;
              break;
            case 'store':
              iconData = Icons.store;
              break;
            case 'local_shipping':
              iconData = Icons.local_shipping;
              break;
            case 'local_offer':
              iconData = Icons.local_offer;
              break;
            case 'local_mall':
              iconData = Icons.local_mall;
              break;
            case 'local_atm':
              iconData = Icons.local_atm;
              break;
            case 'local_bar':
              iconData = Icons.local_bar;
              break;
            case 'local_cafe':
              iconData = Icons.local_cafe;
              break;
            case 'local_dining':
              iconData = Icons.local_dining;
              break;
            case 'local_drink':
              iconData = Icons.local_drink;
              break;
            case 'local_florist':
              iconData = Icons.local_florist;
              break;
            case 'local_gas_station':
              iconData = Icons.local_gas_station;
              break;
            case 'local_grocery_store':
              iconData = Icons.local_grocery_store;
              break;
            case 'local_hospital':
              iconData = Icons.local_hospital;
              break;
            case 'local_hotel':
              iconData = Icons.local_hotel;
              break;
            case 'local_laundry_service':
              iconData = Icons.local_laundry_service;
              break;
            case 'local_library':
              iconData = Icons.local_library;
              break;
            case 'local_movies':
              iconData = Icons.local_movies;
              break;
            case 'local_parking':
              iconData = Icons.local_parking;
              break;
            case 'local_pharmacy':
              iconData = Icons.local_pharmacy;
              break;
            case 'local_phone':
              iconData = Icons.local_phone;
              break;
            case 'local_pizza':
              iconData = Icons.local_pizza;
              break;
            case 'local_post_office':
              iconData = Icons.local_post_office;
              break;
            case 'local_printshop':
              iconData = Icons.local_printshop;
              break;
            case 'local_see':
              iconData = Icons.local_see;
              break;
            case 'local_taxi':
              iconData = Icons.local_taxi;
              break;
            default:
              iconData = Icons.circle;
          }
        } else if (value is int) {
          //iconData = IconData(value, fontFamily: 'MaterialIcons');
        }

        if (iconData != null && icons != null) {
          icons[key.toString()] = iconData;
        }
      });
    }

    // Parse other string values
    final noneOptionLabel = json['noneOptionLabel']?.toString() ?? "None";
    final semanticLabel = json['semanticLabel']?.toString();
    final tooltip = json['tooltip']?.toString();

    // Parse JSON configuration properties
    final useJsonValidation = parseBool(json['useJsonValidation'], false);
    final useJsonStyling = parseBool(json['useJsonStyling'], false);
    final useJsonBehavior = parseBool(json['useJsonBehavior'], false);
    final useJsonOptions = parseBool(json['useJsonOptions'], false);
    final jsonValidationMessage = json['jsonValidationMessage']?.toString();
    final jsonOptionGroupType = json['jsonOptionGroupType']?.toString();

    // Parse JSON callback properties
    final useJsonCallbacks = parseBool(json['useJsonCallbacks'], false);
    Map<String, dynamic>? jsonCallbacks;

    if (json['callbacks'] != null) {
      if (json['callbacks'] is Map) {
        jsonCallbacks = Map<String, dynamic>.from(json['callbacks'] as Map);
      } else if (json['callbacks'] is String) {
        try {
          jsonCallbacks =
              jsonDecode(json['callbacks'] as String) as Map<String, dynamic>;
        } catch (e) {
          debugPrint('Error parsing callbacks JSON: $e');
        }
      }
    }

    // Create the widget with all parsed parameters
    return RadioButtonWidget(
      options: options,
      initialValue: initialValue,
      groupTitle: groupTitle,
      groupTitleAlignment: groupTitleAlignment,
      activeColor: activeColor,
      inactiveColor: inactiveColor,
      fillColor: fillColor,
      textColor: textColor,
      selectedTextColor: selectedTextColor,
      radioSize: radioSize,
      fontSize: fontSize,
      fontWeight: fontWeight,
      selectedFontWeight: selectedFontWeight,
      isHorizontal: isHorizontal,
      isDisabled: isDisabled,
      isReadOnly: isReadOnly,
      spacing: spacing,
      padding: padding,
      margin: margin,
      showDividers: showDividers,
      dividerColor: dividerColor,
      dividerThickness: dividerThickness,
      hasBorder: hasBorder,
      borderColor: borderColor,
      borderWidth: borderWidth,
      borderRadius: borderRadius,
      hasShadow: hasShadow,
      elevation: elevation,
      shadowColor: shadowColor,
      isCard: isCard,
      backgroundColor: backgroundColor,
      showTooltip: showTooltip,
      tooltips: tooltips,
      hasAnimation: hasAnimation,
      animationDuration: animationDuration,
      wrapText: wrapText,
      maxLines: maxLines,
      showIcons: showIcons,
      icons: icons,
      iconSize: iconSize,
      iconColor: iconColor,
      selectedIconColor: selectedIconColor,
      iconPosition: iconPosition,
      iconTextSpacing: iconTextSpacing,
      allowNone: allowNone,
      noneOptionLabel: noneOptionLabel,
      useMaterial3: useMaterial3,
      useCustomShape: useCustomShape,
      radioShape: radioShape,
      isDense: isDense,
      radioPosition: radioPosition,
      optionAlignment: optionAlignment,
      semanticLabel: semanticLabel,
      // Advanced interaction properties
      hoverColor: hoverColor,
      focusColor: focusColor,
      hoverBorderColor: hoverBorderColor,
      selectedBorderColor: selectedBorderColor,
      tooltip: tooltip,
      enableFeedback: enableFeedback,
      autofocus: autofocus,
      // JSON configuration properties
      jsonConfig: json,
      useJsonValidation: useJsonValidation,
      useJsonStyling: useJsonStyling,
      useJsonBehavior: useJsonBehavior,
      useJsonOptions: useJsonOptions,
      jsonValidationMessage: jsonValidationMessage,
      jsonOptionGroupType: jsonOptionGroupType,
      // JSON callback properties
      jsonCallbacks: jsonCallbacks,
      useJsonCallbacks: useJsonCallbacks,
      callbackState: {},
    );
  }

  @override
  State<RadioButtonWidget> createState() => _RadioButtonWidgetState();
}

class _RadioButtonWidgetState extends State<RadioButtonWidget>
    with SingleTickerProviderStateMixin {
  String? _selectedValue;
  late AnimationController _animationController;
  bool _isHovered = false;
  bool _hasFocus = false;
  String? _hoveredOption; // Track which specific option is being hovered
  Map<String, dynamic>? _parsedJsonConfig;
  List<String>? _jsonCustomOptions;
  late Animation<double> _animation;

  // Callback state map
  late Map<String, dynamic> _callbackState;

  @override
  void initState() {
    super.initState();
    _selectedValue = widget.initialValue;

    // Initialize animation controller
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    if (widget.hasAnimation && _selectedValue != null) {
      _animationController.forward();
    }

    // Initialize callback state
    _callbackState =
        widget.callbackState != null
            ? Map<String, dynamic>.from(widget.callbackState!)
            : {};

    // Parse JSON configuration if provided
    if (widget.jsonConfig != null) {
      _parseJsonConfig();
    }
  }

  /// Parses the JSON configuration
  void _parseJsonConfig() {
    try {
      if (widget.jsonConfig is String) {
        _parsedJsonConfig =
            jsonDecode(widget.jsonConfig as String) as Map<String, dynamic>;
      } else if (widget.jsonConfig is Map) {
        _parsedJsonConfig = Map<String, dynamic>.from(widget.jsonConfig as Map);
      }

      // Extract validation rules if needed
      if (widget.useJsonValidation &&
          _parsedJsonConfig != null &&
          _parsedJsonConfig!.containsKey('validationRules')) {
        // Validation rules are processed when needed
        debugPrint('Validation rules found in JSON config');
      }

      // Extract custom options if needed
      if (widget.useJsonOptions &&
          _parsedJsonConfig != null &&
          _parsedJsonConfig!.containsKey('customOptions')) {
        final options = _parsedJsonConfig!['customOptions'];
        if (options is List) {
          _jsonCustomOptions = List<String>.from(
            options.map((option) => option.toString()),
          );
        }
      }
    } catch (e) {
      debugPrint('Error parsing JSON config: $e');
    }
  }

  /// Handles hover state changes
  void _onHoverChange(bool isHovered) {
    setState(() {
      _isHovered = isHovered;

      // Call onHover callback if provided
      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }

      // Execute dynamic callback if enabled
      if (widget.useJsonCallbacks &&
          widget.jsonCallbacks != null &&
          widget.jsonCallbacks!.containsKey('onHover')) {
        final callback = widget.jsonCallbacks!['onHover'];
        CallbackInterpreter.executeCallback(
          callback,
          context,
          value: isHovered,
          state: _callbackState,
          customHandlers: widget.customCallbackHandlers,
        );
      }
    });
  }

  /// Handles focus state changes
  void _onFocusChange(bool hasFocus) {
    setState(() {
      _hasFocus = hasFocus;

      // Call onFocus callback if provided
      if (widget.onFocus != null) {
        widget.onFocus!(hasFocus);
      }

      // Execute dynamic callback if enabled
      if (widget.useJsonCallbacks &&
          widget.jsonCallbacks != null &&
          widget.jsonCallbacks!.containsKey('onFocus')) {
        final callback = widget.jsonCallbacks!['onFocus'];
        CallbackInterpreter.executeCallback(
          callback,
          context,
          value: hasFocus,
          state: _callbackState,
          customHandlers: widget.customCallbackHandlers,
        );
      }
    });
  }

  /// Handles hover state changes for individual options
  void _onOptionHoverChange(String option, bool isHovered) {
    setState(() {
      _hoveredOption = isHovered ? option : null;
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleSelectionChange(String? value) {
    if (widget.isDisabled || widget.isReadOnly) return;

    setState(() {
      // If allowNone is true and the user selects the current value, clear the selection
      if (widget.allowNone && value == _selectedValue) {
        _selectedValue = null;
      } else {
        _selectedValue = value;
      }
    });

    if (widget.hasAnimation) {
      _animationController.reset();
      _animationController.forward();
    }

    // Execute standard callback if provided
    if (widget.onChanged != null) {
      widget.onChanged!(_selectedValue);
    }

    // Execute dynamic callback if enabled
    if (widget.useJsonCallbacks &&
        widget.jsonCallbacks != null &&
        widget.jsonCallbacks!.containsKey('onChanged')) {
      final callback = widget.jsonCallbacks!['onChanged'];
      CallbackInterpreter.executeCallback(
        callback,
        context,
        value: _selectedValue,
        state: _callbackState,
        customHandlers: widget.customCallbackHandlers,
      );
    }
  }

  Widget _buildRadioButton(String option, bool isSelected) {
    final effectiveActiveColor =
        widget.isDisabled
            ? widget.activeColor.withAlpha(128) // 0.5 * 255 = ~128
            : widget.activeColor;

    final effectiveInactiveColor =
        widget.isDisabled
            ? widget.inactiveColor.withAlpha(128) // 0.5 * 255 = ~128
            : widget.inactiveColor;

    // Determine border color based on state
    Color borderColor;
    Color fillColor;

    if (widget.isDisabled) {
      borderColor = isSelected ? effectiveActiveColor : effectiveInactiveColor;
      fillColor = isSelected ? effectiveActiveColor : Colors.transparent;
    } else if (isSelected) {
      // Use selected border color for selected state (red as requested)
      borderColor = widget.selectedBorderColor ?? Color(0xFF0058FF);
      fillColor = widget.selectedBorderColor ?? Color(0xFF0058FF);
    } else if (_hoveredOption == option) {
      // Use hover border color when this specific option is hovered
      borderColor = widget.hoverBorderColor ?? widget.activeColor;
      fillColor = Colors.transparent;
    } else {
      // Default inactive color - grey border, transparent fill
      borderColor = effectiveInactiveColor;
      fillColor = Colors.transparent;
    }

    // Custom radio button shape
    if (widget.useCustomShape) {
      return GestureDetector(
        onTap: () => _handleSelectionChange(option),
        child: Container(
          width: widget.radioSize,
          height: widget.radioSize,
          decoration: BoxDecoration(
            color: fillColor,
            border: Border.all(color: borderColor, width: widget.borderWidth),
            shape:
                widget.radioShape == RadioShape.circle
                    ? BoxShape.circle
                    : BoxShape.rectangle,
            borderRadius:
                widget.radioShape == RadioShape.square
                    ? BorderRadius.circular(widget.borderRadius / 4)
                    : widget.radioShape == RadioShape.diamond
                    ? null
                    : null,
          ),
          transform:
              widget.radioShape == RadioShape.diamond
                  ? Matrix4.rotationZ(0.785398) // 45 degrees in radians
                  : null,
          child:
              isSelected && widget.radioShape == RadioShape.circle
                  ? Center(
                    child: Container(
                      width: widget.radioSize * 0.4,
                      height: widget.radioSize * 0.4,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                    ),
                  )
                  : null,
        ),
      );
    }

    // Custom radio button implementation to match the desired appearance
    return GestureDetector(
      onTap:
          widget.isDisabled || widget.isReadOnly
              ? null
              : () => _handleSelectionChange(option),
      child: Container(
        width: widget.radioSize,
        height: widget.radioSize,
        decoration: BoxDecoration(
          color: fillColor,
          border: Border.all(color: borderColor, width: widget.borderWidth),
          shape: BoxShape.circle,
        ),
        child:
            isSelected
                ? Center(
                  child: Container(
                    width: widget.radioSize * 0.4,
                    height: widget.radioSize * 0.4,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                  ),
                )
                : null,
      ),
    );
  }

  Widget _buildOptionLabel(String option, bool isSelected) {
    final effectiveTextColor =
        widget.isDisabled
            ? widget.textColor.withAlpha(128) // 0.5 * 255 = ~128
            : isSelected
            ? widget.selectedTextColor!
            : widget.textColor;

    final effectiveFontWeight =
        isSelected && widget.selectedFontWeight != null
            ? widget.selectedFontWeight!
            : widget.fontWeight;

    return Text(
      option,
      // style: TextStyle(
      //   color: effectiveTextColor,
      //   fontSize: _getResponsiveValueFontSize(context),
      //   fontWeight: effectiveFontWeight,
      //   // Force the color to ensure it's not being overridden
      //   inherit: false,
      // ),
      style: FontManager.getCustomStyle(
        fontFamily: FontManager.fontFamilyInter,
        fontWeight: FontManager.medium,
        //color: effectiveTextColor,
        color: Color(0xFF333333),
        fontSize: _getResponsiveValueFontSize(context),

        // Force the color to ensure it's not being overridden
        //inherit: false, // ✅ Now valid
      ),
      softWrap: widget.wrapText,
      maxLines: widget.maxLines,
      overflow: widget.maxLines != null ? TextOverflow.ellipsis : null,
    );
  }

  Widget _buildIcon(String option, bool isSelected) {
    if (!widget.showIcons) return const SizedBox.shrink();

    final IconData iconData =
        widget.icons != null && widget.icons!.containsKey(option)
            ? widget.icons![option]!
            : Icons.circle;

    final effectiveIconColor =
        widget.isDisabled
            ? (widget.iconColor ?? widget.textColor).withAlpha(
              128,
            ) // 0.5 * 255 = ~128
            : isSelected && widget.selectedIconColor != null
            ? widget.selectedIconColor!
            : widget.iconColor ?? widget.textColor;

    return Icon(iconData, size: widget.iconSize, color: effectiveIconColor);
  }

  Widget _buildOption(String option) {
  final isSelected = option == _selectedValue;

  // Create the option content
  Widget optionContent;

  // Determine the order of radio button, icon, and label based on configuration
  List<Widget> rowChildren = [];

  // Add radio button if it's in the leading position
  if (widget.radioPosition == RadioPosition.leading) {
    rowChildren.add(_buildRadioButton(option, isSelected));
    rowChildren.add(SizedBox(width: widget.spacing));
  }

  // Create icon and label group
  Widget textWithIcon;
  if (widget.showIcons) {
    if (widget.iconPosition == IconPosition.leading) {
      textWithIcon = Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildIcon(option, isSelected),
          SizedBox(width: widget.iconTextSpacing),
          Flexible(child: _buildOptionLabel(option, isSelected)),
        ],
      );
    } else {
      textWithIcon = Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(child: _buildOptionLabel(option, isSelected)),
          SizedBox(width: widget.iconTextSpacing),
          _buildIcon(option, isSelected),
        ],
      );
    }
  } else {
    textWithIcon = Flexible(child: _buildOptionLabel(option, isSelected));
  }

  rowChildren.add(textWithIcon);

  // Add radio button if it's in the trailing position
  if (widget.radioPosition == RadioPosition.trailing) {
    rowChildren.add(SizedBox(width: widget.spacing));
    rowChildren.add(_buildRadioButton(option, isSelected));
  }

  // Create the row with all elements
  optionContent = Row(
    mainAxisSize: MainAxisSize.min,
    mainAxisAlignment: widget.optionAlignment,
    children: rowChildren,
  );

  // Apply animation if enabled
  if (widget.hasAnimation && isSelected) {
    optionContent = FadeTransition(opacity: _animation, child: optionContent);
  }

  // Apply tooltip if enabled
  if (widget.showTooltip) {
    final tooltipText =
        widget.tooltips != null && widget.tooltips!.containsKey(option)
            ? widget.tooltips![option]!
            : option;

    optionContent = Tooltip(message: tooltipText, child: optionContent);
  }

  // Make the whole row clickable
  optionContent = GestureDetector(
    behavior: HitTestBehavior.translucent,
    onTap: widget.isDisabled || widget.isReadOnly
        ? null
        : () => _handleSelectionChange(option),
    child: optionContent,
  );

  // Wrap in a container with padding and add per-option hover handling
  return MouseRegion(
    onEnter: (_) => _onOptionHoverChange(option, true),
    onExit: (_) => _onOptionHoverChange(option, false),
    child: Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: optionContent,
    ),
  );
}
  @override
  Widget build(BuildContext context) {
    // Create the list of options
    List<String> displayOptions;

    // Use custom options from JSON if available
    if (widget.useJsonOptions &&
        _jsonCustomOptions != null &&
        _jsonCustomOptions!.isNotEmpty) {
      displayOptions = List.from(_jsonCustomOptions!);
    } else {
      displayOptions = List.from(widget.options);
    }

    // Add "None" option if allowNone is true
    if (widget.allowNone && !displayOptions.contains(widget.noneOptionLabel)) {
      displayOptions.insert(0, widget.noneOptionLabel);
    }

    // Create the list of option widgets
    List<Widget> optionWidgets = [];

    for (int i = 0; i < displayOptions.length; i++) {
      optionWidgets.add(_buildOption(displayOptions[i]));

      // Add divider if needed (except after the last item)
      if (widget.showDividers && i < displayOptions.length - 1) {
        optionWidgets.add(
          Divider(
            color: widget.dividerColor ?? widget.inactiveColor,
            thickness: widget.dividerThickness,
            height: widget.spacing,
          ),
        );
      }
    }

    // Create the group title if provided
    Widget? titleWidget;
    if (widget.groupTitle != null) {
      titleWidget = Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: Text(
          widget.groupTitle!,
          style:
              widget.groupTitleStyle ??
              TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: _getResponsiveFontSize(context),
              ),
          textAlign: widget.groupTitleAlignment,
        ),
      );
    }

    // Create the main content
    Widget content;
    if (widget.isHorizontal) {
      content = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (titleWidget != null) titleWidget,
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(mainAxisSize: MainAxisSize.min, children: optionWidgets),
          ),
        ],
      );
    } else {
      content = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [if (titleWidget != null) titleWidget, ...optionWidgets],
      );
    }

    // Apply container styling
    Widget styledContent = Container(
      margin: widget.margin,
      decoration: BoxDecoration(
        //color: widget.backgroundColor,
        //borderRadius: BorderRadius.circular(widget.borderRadius),
        border:
            widget.hasBorder
                ? Border.all(
                  color: widget.borderColor,
                  width: widget.borderWidth,
                )
                : null,
        boxShadow:
            widget.hasShadow
                ? [
                  BoxShadow(
                    color:
                        widget.shadowColor ??
                        Colors.black.withAlpha(26), // 0.1 * 255 = ~26
                    blurRadius: widget.elevation,
                    offset: const Offset(0, 2),
                  ),
                ]
                : null,
      ),
      child: Padding(padding: const EdgeInsets.all(0.0), child: content),
    );

    // Apply card styling if requested
    if (widget.isCard) {
      styledContent = Card(
        elevation: widget.hasShadow ? widget.elevation : 0,
        color: widget.backgroundColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(widget.borderRadius),
          side:
              widget.hasBorder
                  ? BorderSide(
                    color: widget.borderColor,
                    width: widget.borderWidth,
                  )
                  : BorderSide.none,
        ),
        shadowColor: widget.shadowColor,
        margin: widget.margin,
        child: Padding(padding: const EdgeInsets.all(8.0), child: content),
      );
    }

    // Apply semantics
    if (widget.semanticLabel != null) {
      styledContent = Semantics(
        label: widget.semanticLabel,
        child: styledContent,
      );
    }

    // Apply tooltip if provided
    if (widget.tooltip != null) {
      styledContent = Tooltip(message: widget.tooltip!, child: styledContent);
    }

    // Apply focus and hover handling
    return MouseRegion(
      onEnter: (_) => _onHoverChange(true),
      onExit: (_) => _onHoverChange(false),
      cursor:
          widget.isDisabled
              ? SystemMouseCursors.forbidden
              : SystemMouseCursors.click,
      child: Focus(
        focusNode: widget.focusNode,
        autofocus: widget.autofocus,
        onFocusChange: _onFocusChange,
        child: GestureDetector(
          onTap: () {
            // Execute standard callback if provided
            if (widget.onTap != null) {
              widget.onTap!();
            }

            // Execute dynamic callback if enabled
            if (widget.useJsonCallbacks &&
                widget.jsonCallbacks != null &&
                widget.jsonCallbacks!.containsKey('onTap')) {
              final callback = widget.jsonCallbacks!['onTap'];
              CallbackInterpreter.executeCallback(
                callback,
                context,
                value: _selectedValue,
                state: _callbackState,
                customHandlers: widget.customCallbackHandlers,
              );
            }
          },
          onDoubleTap: () {
            // Execute standard callback if provided
            if (widget.onDoubleTap != null) {
              widget.onDoubleTap!();
            }

            // Execute dynamic callback if enabled
            if (widget.useJsonCallbacks &&
                widget.jsonCallbacks != null &&
                widget.jsonCallbacks!.containsKey('onDoubleTap')) {
              final callback = widget.jsonCallbacks!['onDoubleTap'];
              CallbackInterpreter.executeCallback(
                callback,
                context,
                value: _selectedValue,
                state: _callbackState,
                customHandlers: widget.customCallbackHandlers,
              );
            }
          },
          onLongPress: () {
            // Execute standard callback if provided
            if (widget.onLongPress != null) {
              widget.onLongPress!();
            }

            // Execute dynamic callback if enabled
            if (widget.useJsonCallbacks &&
                widget.jsonCallbacks != null &&
                widget.jsonCallbacks!.containsKey('onLongPress')) {
              final callback = widget.jsonCallbacks!['onLongPress'];
              CallbackInterpreter.executeCallback(
                callback,
                context,
                value: _selectedValue,
                state: _callbackState,
                customHandlers: widget.customCallbackHandlers,
              );
            }
          },
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            decoration: BoxDecoration(
              color:
                  _isHovered
                      ? (widget.hoverColor ?? Colors.transparent)
                      : (_hasFocus
                          ? (widget.focusColor ?? Colors.transparent)
                          : Colors.transparent),
              borderRadius: BorderRadius.circular(widget.borderRadius),
            ),
            child: styledContent,
          ),
        ),
      ),
    );
  }
}

double _getResponsiveFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 16.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 14.0; // Large
  } else if (screenWidth >= 1280) {
    return 12.0; // Medium
  } else {
    return 12.0; // Default for very small screens
  }
}

double _getResponsiveValueFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 18.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 16.0; // Large
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium
  } else {
    return 14.0; // Default for very small screens
  }
}
