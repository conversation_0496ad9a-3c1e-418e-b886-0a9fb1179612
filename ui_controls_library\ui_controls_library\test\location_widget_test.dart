import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:ui_controls_library/widgets/location_widget.dart';

void main() {
  group('Enhanced LocationWidget Tests', () {
    testWidgets('LocationWidget displays address field', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: LocationWidget(
              displayMode: LocationDisplayMode.addressOnly,
              label: 'Test Location',
              addressHint: 'Enter address',
            ),
          ),
        ),
      );

      // Verify the label is displayed
      expect(find.text('Test Location'), findsOneWidget);
      
      // Verify the address field is displayed
      expect(find.byType(TextField), findsOneWidget);
      
      // Verify the hint text
      expect(find.text('Enter address'), findsOneWidget);
    });

    testWidgets('LocationWidget shows search icon', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: LocationWidget(
              displayMode: LocationDisplayMode.addressOnly,
              enableAddressSearch: true,
            ),
          ),
        ),
      );

      // Verify the search icon is present (as suffix icon)
      expect(find.byType(GestureDetector), findsWidgets);
    });

    testWidgets('LocationWidget calls onAddressChanged when text changes', (WidgetTester tester) async {
      String? changedAddress;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: LocationWidget(
              displayMode: LocationDisplayMode.addressOnly,
              onAddressChanged: (address) {
                changedAddress = address;
              },
            ),
          ),
        ),
      );

      // Enter text in the address field
      await tester.enterText(find.byType(TextField), 'Test Address');
      await tester.pump();

      // Verify the callback was called
      expect(changedAddress, equals('Test Address'));
    });

    testWidgets('LocationWidget displays map when coordinates are set', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: LocationWidget(
              displayMode: LocationDisplayMode.mapAndAddress,
              initialLatitude: 17.3850,
              initialLongitude: 78.4867,
            ),
          ),
        ),
      );

      // Verify the map is displayed
      expect(find.text('No location selected'), findsNothing);
    });

    testWidgets('LocationWidget shows map in addressOnly mode when location is found', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: LocationWidget(
              displayMode: LocationDisplayMode.addressOnly,
              initialLatitude: 17.3850,
              initialLongitude: 78.4867,
            ),
          ),
        ),
      );

      // The map should be shown even in addressOnly mode when coordinates are available
      // This tests our enhancement where map appears dynamically
      await tester.pump();
      
      // Verify that the widget builds without errors
      expect(find.byType(LocationWidget), findsOneWidget);
    });

    testWidgets('LocationWidget handles different display modes', (WidgetTester tester) async {
      // Test mapOnly mode
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: LocationWidget(
              displayMode: LocationDisplayMode.mapOnly,
              initialLatitude: 17.3850,
              initialLongitude: 78.4867,
            ),
          ),
        ),
      );

      await tester.pump();
      expect(find.byType(LocationWidget), findsOneWidget);

      // Test addressOnly mode
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: LocationWidget(
              displayMode: LocationDisplayMode.addressOnly,
            ),
          ),
        ),
      );

      await tester.pump();
      expect(find.byType(TextField), findsOneWidget);

      // Test mapAndAddress mode
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: LocationWidget(
              displayMode: LocationDisplayMode.mapAndAddress,
              initialLatitude: 17.3850,
              initialLongitude: 78.4867,
            ),
          ),
        ),
      );

      await tester.pump();
      expect(find.byType(LocationWidget), findsOneWidget);
    });

    testWidgets('LocationWidget respects readOnly property', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: LocationWidget(
              displayMode: LocationDisplayMode.addressOnly,
              readOnly: true,
            ),
          ),
        ),
      );

      final textField = tester.widget<TextField>(find.byType(TextField));
      expect(textField.readOnly, isTrue);
    });

    testWidgets('LocationWidget shows error message', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: LocationWidget(
              displayMode: LocationDisplayMode.addressOnly,
            ),
          ),
        ),
      );

      // The error message display is handled internally by the widget
      // This test ensures the widget builds correctly
      expect(find.byType(LocationWidget), findsOneWidget);
    });
  });
}
