// To parse this JSON data, do
//
//     final createGetProjectsModel = createGetProjectsModelFromJson(jsonString);

import 'dart:convert';

CreateGetProjectsModel createGetProjectsModelFromJson(String str) =>
    CreateGetProjectsModel.fromJson(json.decode(str));

String createGetProjectsModelToJson(CreateGetProjectsModel data) =>
    json.encode(data.toJson());

class CreateGetProjectsModel {
  String? tenant;
  List<Project>? projects;
  int? totalProjects;
  int? activeProjects;
  int? completedProjects;

  CreateGetProjectsModel({
    this.tenant,
    this.projects,
    this.totalProjects,
    this.activeProjects,
    this.completedProjects,
  });

  CreateGetProjectsModel copyWith({
    String? tenant,
    List<Project>? projects,
    int? totalProjects,
    int? activeProjects,
    int? completedProjects,
  }) =>
      CreateGetProjectsModel(
        tenant: tenant ?? this.tenant,
        projects: projects ?? this.projects,
        totalProjects: totalProjects ?? this.totalProjects,
        activeProjects: activeProjects ?? this.activeProjects,
        completedProjects: completedProjects ?? this.completedProjects,
      );

  factory CreateGetProjectsModel.fromJson(Map<String, dynamic> json) =>
      CreateGetProjectsModel(
        tenant: json["tenant"],
        projects: json["projects"] == null
            ? []
            : List<Project>.from(
                json["projects"]!.map((x) => Project.fromJson(x))),
        totalProjects: json["total_projects"],
        activeProjects: json["active_projects"],
        completedProjects: json["completed_projects"],
      );

  Map<String, dynamic> toJson() => {
        "tenant": tenant,
        "projects": projects == null
            ? []
            : List<dynamic>.from(projects!.map((x) => x.toJson())),
        "total_projects": totalProjects,
        "active_projects": activeProjects,
        "completed_projects": completedProjects,
      };
}

class Project {
  String? projectId;
  String? projectName;
  String? tenant;
  String? status;
  DateTime? createdAt;
  DateTime? lastUpdated;
  List<Conversation>? conversations;
  int? totalMessages;
  int? completionPercentage;
  int? conversationsCount;
  ArtifactsGenerated? artifactsGenerated;

  Project({
    this.projectId,
    this.projectName,
    this.tenant,
    this.status,
    this.createdAt,
    this.lastUpdated,
    this.conversations,
    this.totalMessages,
    this.completionPercentage,
    this.conversationsCount,
    this.artifactsGenerated,
  });

  Project copyWith({
    String? projectId,
    String? projectName,
    String? tenant,
    String? status,
    DateTime? createdAt,
    DateTime? lastUpdated,
    List<Conversation>? conversations,
    int? totalMessages,
    int? completionPercentage,
    int? conversationsCount,
    ArtifactsGenerated? artifactsGenerated,
  }) =>
      Project(
        projectId: projectId ?? this.projectId,
        projectName: projectName ?? this.projectName,
        tenant: tenant ?? this.tenant,
        status: status ?? this.status,
        createdAt: createdAt ?? this.createdAt,
        lastUpdated: lastUpdated ?? this.lastUpdated,
        conversations: conversations ?? this.conversations,
        totalMessages: totalMessages ?? this.totalMessages,
        completionPercentage: completionPercentage ?? this.completionPercentage,
        conversationsCount: conversationsCount ?? this.conversationsCount,
        artifactsGenerated: artifactsGenerated ?? this.artifactsGenerated,
      );

  factory Project.fromJson(Map<String, dynamic> json) => Project(
        projectId: json["project_id"],
        projectName: json["project_name"],
        tenant: json["tenant"],
        status: json["status"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        lastUpdated: json["last_updated"] == null
            ? null
            : DateTime.parse(json["last_updated"]),
        conversations: json["conversations"] == null
            ? []
            : List<Conversation>.from(
                json["conversations"]!.map((x) => Conversation.fromJson(x))),
        totalMessages: json["total_messages"],
        completionPercentage: json["completion_percentage"],
        conversationsCount: json["conversations_count"],
        artifactsGenerated: json["artifacts_generated"] == null
            ? null
            : ArtifactsGenerated.fromJson(json["artifacts_generated"]),
      );

  Map<String, dynamic> toJson() => {
        "project_id": projectId,
        "project_name": projectName,
        "tenant": tenant,
        "status": status,
        "created_at": createdAt?.toIso8601String(),
        "last_updated": lastUpdated?.toIso8601String(),
        "conversations": conversations == null
            ? []
            : List<dynamic>.from(conversations!.map((x) => x.toJson())),
        "total_messages": totalMessages,
        "completion_percentage": completionPercentage,
        "conversations_count": conversationsCount,
        "artifacts_generated": artifactsGenerated?.toJson(),
      };
}

class ArtifactsGenerated {
  bool? brdDocument;
  int? roles;
  int? entities;
  int? localObjectives;
  int? globalObjectives;

  ArtifactsGenerated({
    this.brdDocument,
    this.roles,
    this.entities,
    this.localObjectives,
    this.globalObjectives,
  });

  ArtifactsGenerated copyWith({
    bool? brdDocument,
    int? roles,
    int? entities,
    int? localObjectives,
    int? globalObjectives,
  }) =>
      ArtifactsGenerated(
        brdDocument: brdDocument ?? this.brdDocument,
        roles: roles ?? this.roles,
        entities: entities ?? this.entities,
        localObjectives: localObjectives ?? this.localObjectives,
        globalObjectives: globalObjectives ?? this.globalObjectives,
      );

  factory ArtifactsGenerated.fromJson(Map<String, dynamic> json) =>
      ArtifactsGenerated(
        brdDocument: json["brd_document"],
        roles: json["roles"],
        entities: json["entities"],
        localObjectives: json["local_objectives"],
        globalObjectives: json["global_objectives"],
      );

  Map<String, dynamic> toJson() => {
        "brd_document": brdDocument,
        "roles": roles,
        "entities": entities,
        "local_objectives": localObjectives,
        "global_objectives": globalObjectives,
      };
}

class Conversation {
  String? conversationId;
  String? userId;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? currentPhase;
  String? status;
  int? messageCount;

  Conversation({
    this.conversationId,
    this.userId,
    this.createdAt,
    this.updatedAt,
    this.currentPhase,
    this.status,
    this.messageCount,
  });

  Conversation copyWith({
    String? conversationId,
    String? userId,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? currentPhase,
    String? status,
    int? messageCount,
  }) =>
      Conversation(
        conversationId: conversationId ?? this.conversationId,
        userId: userId ?? this.userId,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        currentPhase: currentPhase ?? this.currentPhase,
        status: status ?? this.status,
        messageCount: messageCount ?? this.messageCount,
      );

  factory Conversation.fromJson(Map<String, dynamic> json) => Conversation(
        conversationId: json["conversation_id"],
        userId: json["user_id"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        currentPhase: json["current_phase"],
        status: json["status"],
        messageCount: json["message_count"],
      );

  Map<String, dynamic> toJson() => {
        "conversation_id": conversationId,
        "user_id": userId,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "current_phase": currentPhase,
        "status": status,
        "message_count": messageCount,
      };
}
